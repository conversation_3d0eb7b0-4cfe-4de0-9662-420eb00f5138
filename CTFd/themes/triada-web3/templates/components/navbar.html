<nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: rgba(0, 0, 0, 0.95); backdrop-filter: blur(20px); border-bottom: 1px solid rgba(220, 38, 38, 0.3);">
    <div class="container">
        <!-- Brand -->
        <a href="{{ url_for('views.static_html', route='/') }}" class="navbar-brand d-flex align-items-center">
            {% if Configs.ctf_logo %}
                <img class="img-responsive ctf_logo me-2" src="{{ url_for('views.files', path=Configs.ctf_logo) }}" height="32" alt="{{ Configs.ctf_name }}" style="filter: drop-shadow(0 0 10px rgba(220, 38, 38, 0.5));">
            {% endif %}
            <span style="font-weight: 800; font-size: 1.5rem; background: var(--triada-gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; text-transform: uppercase; letter-spacing: 0.1em;">
                TRIADA CTF
            </span>
        </a>

        <!-- Mobile Toggle -->
        <button class="navbar-toggler border-0" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation" style="background: rgba(220, 38, 38, 0.1); border-radius: var(--triada-radius-md);">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- Navigation Links -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mr-auto">
                {% for page in Plugins.user_menu_pages %}
                    <li class="nav-item">
                        <a class="nav-link px-3" href="{{ page.route }}" style="font-weight: 500; transition: all 0.3s ease; position: relative;">
                            {{ page.title }}
                        </a>
                    </li>
                {% endfor %}

                {% if Configs.account_visibility != 'admins' %}
                    <li class="nav-item">
                        <a class="nav-link px-3" href="{{ url_for('users.listing') }}" style="font-weight: 500; transition: all 0.3s ease; position: relative;">
                            <i class="fas fa-users me-1"></i>Users
                        </a>
                    </li>
                    {% if Configs.user_mode == 'teams' %}
                    <li class="nav-item">
                        <a class="nav-link px-3" href="{{ url_for('teams.listing') }}" style="font-weight: 500; transition: all 0.3s ease; position: relative;">
                            <i class="fas fa-user-friends me-1"></i>Teams
                        </a>
                    </li>
                    {% endif %}
                {% endif %}

                {% if Configs.account_visibility != 'admins' and Configs.score_visibility != 'admins' %}
                    <li class="nav-item">
                        <a class="nav-link px-3" href="{{ url_for('scoreboard.listing') }}" style="font-weight: 500; transition: all 0.3s ease; position: relative;">
                            <i class="fas fa-trophy me-1"></i>Scoreboard
                        </a>
                    </li>
                {% endif %}

                <li class="nav-item">
                    <a class="nav-link px-3" href="{{ url_for('challenges.listing') }}" style="font-weight: 500; transition: all 0.3s ease; position: relative;">
                        <i class="fas fa-flag me-1"></i>Challenges
                    </a>
                </li>
            </ul>

            <!-- User Menu -->
            <ul class="navbar-nav ml-auto">
                {% if authed() %}
                    {% if is_admin() %}
                        <li class="nav-item">
                            <a class="nav-link px-3" href="{{ url_for('admin.view') }}" style="color: var(--triada-secondary) !important; font-weight: 600;">
                                <i class="fas fa-cog me-1"></i>
                                <span class="d-none d-lg-inline">Admin Panel</span>
                            </a>
                        </li>
                    {% endif %}
                    
                    <li class="nav-item">
                        <a class="nav-link px-3 position-relative" href="{{ url_for('views.notifications') }}">
                            <i class="fas fa-bell"></i>
                            <span class="badge badge-danger badge-pill position-absolute" style="top: 8px; right: 8px; font-size: 0.6rem; min-width: 16px; height: 16px; line-height: 16px; padding: 0;">
                                <span class="badge-notification"></span>
                            </span>
                            <span class="d-none d-lg-inline ms-1">Notifications</span>
                        </a>
                    </li>

                    {% if Configs.user_mode == "teams" %}
                    <li class="nav-item">
                        <a class="nav-link px-3" href="{{ url_for('teams.private') }}">
                            <i class="fas fa-users me-1"></i>
                            <span class="d-none d-lg-inline">Team</span>
                        </a>
                    </li>
                    {% endif %}

                    <!-- User Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle px-3" href="#" id="userDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="background: rgba(0, 212, 255, 0.1); border-radius: var(--triada-radius-md); margin-left: 0.5rem;">
                            <i class="fas fa-user-circle me-1"></i>
                            <span class="d-none d-lg-inline">{{ User.name }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown" style="background: var(--triada-gradient-card); border: 1px solid rgba(220, 38, 38, 0.3); border-radius: var(--triada-radius-md); backdrop-filter: blur(20px); box-shadow: var(--triada-shadow-lg); min-width: 200px;">
                            <a class="dropdown-item" href="{{ url_for('users.private') }}" style="color: var(--triada-text-secondary); transition: all 0.3s ease;">
                                <i class="fas fa-user me-2"></i>Profile
                            </a>
                            <a class="dropdown-item" href="{{ url_for('views.settings') }}" style="color: var(--triada-text-secondary); transition: all 0.3s ease;">
                                <i class="fas fa-cogs me-2"></i>Settings
                            </a>
                            <div class="dropdown-divider" style="border-color: rgba(220, 38, 38, 0.2);"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}" style="color: var(--triada-danger); transition: all 0.3s ease;">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </li>
                {% else %}
                    {% if registration_visible() %}
                        <li class="nav-item">
                            <a class="nav-link px-3" href="{{ url_for('auth.register') }}" style="background: rgba(220, 38, 38, 0.1); border-radius: var(--triada-radius-md); margin-right: 0.5rem; font-weight: 600;">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link px-3" href="{{ url_for('auth.login') }}" style="background: var(--triada-gradient-primary); border-radius: var(--triada-radius-md); font-weight: 600; color: white !important;">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>

<style>
/* Enhanced navbar styles */
.navbar-nav .nav-link {
    color: var(--triada-text-secondary) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    border-radius: var(--triada-radius-sm);
}

.navbar-nav .nav-link:hover {
    color: var(--triada-primary) !important;
    background: rgba(220, 38, 38, 0.1);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--triada-gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

.dropdown-item:hover {
    background: rgba(220, 38, 38, 0.1) !important;
    color: var(--triada-primary) !important;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 38, 38, 0.25);
}

/* Mobile responsive adjustments */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: rgba(0, 0, 0, 0.98);
        border-radius: var(--triada-radius-md);
        margin-top: 1rem;
        padding: 1rem;
        border: 1px solid rgba(220, 38, 38, 0.3);
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        border-radius: var(--triada-radius-md);
    }

    .dropdown-menu {
        background: rgba(26, 26, 26, 0.95) !important;
        border: 1px solid rgba(220, 38, 38, 0.3);
        margin-top: 0.5rem;
    }
}

/* Notification badge animation */
.badge-notification {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Navbar brand hover effect */
.navbar-brand:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.navbar-brand img {
    transition: filter 0.3s ease;
}

.navbar-brand:hover img {
    filter: drop-shadow(0 0 15px rgba(220, 38, 38, 0.8));
}
</style>
