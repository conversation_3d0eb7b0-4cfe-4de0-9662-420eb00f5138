<!DOCTYPE html>
<html lang="en">
<head>
    <title>{{ Configs.ctf_name }} - TRIADA CTF</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="TRIADA CTF - Challenge Your Skills, Secure the Flag. Professional cybersecurity capture the flag competition platform with Web3-inspired design.">
    <meta name="theme-color" content="#00d4ff">
    <meta name="keywords" content="CTF, cybersecurity, hacking, challenges, web3, blockchain, security">
    <meta name="author" content="TRIADA CTF">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:title" content="{{ Configs.ctf_name }} - TRIADA CTF">
    <meta property="og:description" content="Challenge Your Skills, Secure the Flag. Professional cybersecurity capture the flag competition platform.">
    <meta property="og:image" content="{{ url_for('views.themes', path='img/triada-og-image.png') }}">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ request.url }}">
    <meta property="twitter:title" content="{{ Configs.ctf_name }} - TRIADA CTF">
    <meta property="twitter:description" content="Challenge Your Skills, Secure the Flag. Professional cybersecurity capture the flag competition platform.">
    <meta property="twitter:image" content="{{ url_for('views.themes', path='img/triada-og-image.png') }}">

    <link rel="shortcut icon" href="{{ Configs.ctf_small_icon }}" type="image/x-icon">

    <!-- Modern Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    
    <!-- Custom Web3 Theme -->
    <link rel="stylesheet" href="{{ url_for('views.themes', path='css/web3-theme.css') }}">
    
    {% block stylesheets %}
    {% endblock %}

    {{ Plugins.styles }}
    
    <script type="text/javascript">
        var init = {
            'urlRoot': "{{ request.script_root }}",
            'csrfNonce': "{{ Session.nonce }}",
            'userMode': "{{ Configs.user_mode }}",
            'userId': {{ Session.id }},
            'userName': {{ User.name | tojson }},
            'userEmail': {{ User.email | tojson }},
            'teamId': {{ Team.id | tojson }}, 
            'teamName': {{ Team.name | tojson }},
            'start': {{ Configs.start | tojson }},
            'end': {{ Configs.end | tojson }},
            'theme_settings': {{ Configs.theme_settings | tojson }}
        }
    </script>
    {{ Configs.theme_header }}
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="position-fixed w-100 h-100 d-flex align-items-center justify-content-center" style="background: var(--triada-dark-bg); z-index: 9999; top: 0; left: 0;">
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="sr-only">Loading...</span>
            </div>
            <h5 class="text-primary">Loading TRIADA CTF...</h5>
        </div>
    </div>

    {% include "components/navbar.html" %}

    <main role="main" class="fade-in-up">
        {% block content %}
        {% endblock %}
    </main>

    <footer class="footer">
        <div class="container text-center">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3" style="background: var(--triada-gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">TRIADA CTF</h5>
                    <p class="text-muted">Challenge Your Skills, Secure the Flag</p>
                </div>
                <div class="col-md-6">
                    <div class="social-links mb-3">
                        <a href="#" class="text-primary mx-2" style="font-size: 1.5rem;"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-primary mx-2" style="font-size: 1.5rem;"><i class="fab fa-github"></i></a>
                        <a href="#" class="text-primary mx-2" style="font-size: 1.5rem;"><i class="fab fa-discord"></i></a>
                        <a href="#" class="text-primary mx-2" style="font-size: 1.5rem;"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
            </div>
            <hr style="border-color: rgba(0, 212, 255, 0.2);">
            <div class="row">
                <div class="col-md-6 text-md-left">
                    <small style="color: var(--triada-text-muted);">© 2024 TRIADA CTF. All rights reserved.</small>
                </div>
                <div class="col-md-6 text-md-right">
                    <a href="https://ctfd.io" style="color: var(--triada-text-muted); transition: color 0.3s ease;">
                        <small>Powered by CTFd</small>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script defer src="{{ url_for('views.themes', path='js/vendor.bundle.js') }}"></script>
    <script defer src="{{ url_for('views.themes', path='js/core.js') }}"></script>
    <script defer src="{{ url_for('views.themes', path='js/helpers.js') }}"></script>

    {% block entrypoint %}
    <script defer src="{{ url_for('views.themes', path='js/pages/main.js') }}"></script>
    {% endblock %}

    {% block scripts %}
    {% endblock %}

    {{ Plugins.scripts }}

    <!-- Custom Web3 Scripts -->
    <script>
        // Loading screen
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.getElementById('loading-screen').style.opacity = '0';
                setTimeout(function() {
                    document.getElementById('loading-screen').style.display = 'none';
                }, 300);
            }, 500);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add fade-in animation to cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        // Observe all cards
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.card, .challenge-card').forEach(card => {
                observer.observe(card);
            });
        });

        // Particle effect for background (optional)
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.position = 'fixed';
            particle.style.width = '2px';
            particle.style.height = '2px';
            particle.style.background = 'rgba(0, 212, 255, 0.5)';
            particle.style.borderRadius = '50%';
            particle.style.pointerEvents = 'none';
            particle.style.zIndex = '-1';
            particle.style.left = Math.random() * window.innerWidth + 'px';
            particle.style.top = window.innerHeight + 'px';
            particle.style.animation = 'float-up 10s linear infinite';
            
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 10000);
        }

        // Add CSS for particle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float-up {
                to {
                    transform: translateY(-${window.innerHeight + 100}px);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Create particles periodically
        setInterval(createParticle, 2000);
    </script>

    {{ Configs.theme_footer }}
</body>
</html>
