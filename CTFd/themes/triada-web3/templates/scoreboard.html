{% extends "base.html" %}

{% block stylesheets %}
<style>
    .scoreboard-hero {
        background: var(--triada-gradient-dark);
        padding: 4rem 0 2rem;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    .scoreboard-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 30% 20%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 70% 80%, rgba(124, 58, 237, 0.15) 0%, transparent 50%);
        z-index: 1;
    }

    .scoreboard-hero .container {
        position: relative;
        z-index: 2;
    }

    .scoreboard-hero h1 {
        font-size: 4rem;
        font-weight: 900;
        text-align: center;
        margin-bottom: 1rem;
        background: var(--triada-gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-transform: uppercase;
        letter-spacing: 0.1em;
    }

    .scoreboard-hero p {
        text-align: center;
        font-size: 1.25rem;
        color: var(--triada-text-secondary);
        max-width: 600px;
        margin: 0 auto;
    }

    .scoreboard-filters {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 3rem;
        flex-wrap: wrap;
    }

    .filter-btn {
        background: rgba(26, 26, 46, 0.8);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-md);
        padding: 0.75rem 1.5rem;
        color: var(--triada-text-secondary);
        text-decoration: none;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .filter-btn:hover,
    .filter-btn.active {
        background: var(--triada-gradient-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--triada-shadow-md);
        text-decoration: none;
    }

    .podium {
        display: flex;
        justify-content: center;
        align-items: end;
        gap: 2rem;
        margin-bottom: 4rem;
        flex-wrap: wrap;
    }

    .podium-place {
        background: var(--triada-gradient-card);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-lg);
        padding: 2rem;
        text-align: center;
        backdrop-filter: blur(20px);
        position: relative;
        min-width: 200px;
        transition: all 0.3s ease;
    }

    .podium-place:hover {
        transform: translateY(-5px);
        box-shadow: var(--triada-shadow-xl);
    }

    .podium-first {
        order: 2;
        border-color: #ffd700;
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
    }

    .podium-second {
        order: 1;
        border-color: #c0c0c0;
        box-shadow: 0 0 20px rgba(192, 192, 192, 0.3);
    }

    .podium-third {
        order: 3;
        border-color: #cd7f32;
        box-shadow: 0 0 20px rgba(205, 127, 50, 0.3);
    }

    .podium-rank {
        font-size: 3rem;
        font-weight: 900;
        margin-bottom: 1rem;
    }

    .podium-first .podium-rank { color: #ffd700; }
    .podium-second .podium-rank { color: #c0c0c0; }
    .podium-third .podium-rank { color: #cd7f32; }

    .podium-name {
        font-size: 1.5rem;
        font-weight: 700;
        background: var(--triada-gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
    }

    .podium-score {
        font-size: 2rem;
        font-weight: 800;
        color: var(--triada-secondary);
        margin-bottom: 0.5rem;
    }

    .podium-team {
        color: var(--triada-text-muted);
        font-size: 0.875rem;
    }

    .scoreboard-table {
        background: var(--triada-gradient-card);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-lg);
        backdrop-filter: blur(20px);
        overflow: hidden;
        box-shadow: var(--triada-shadow-lg);
    }

    .table {
        margin-bottom: 0;
        color: var(--triada-text-primary);
    }

    .table thead th {
        background: rgba(0, 212, 255, 0.1);
        border-color: rgba(0, 212, 255, 0.2);
        color: var(--triada-text-primary);
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 0.875rem;
        padding: 1.5rem 1rem;
        border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    }

    .table tbody td {
        border-color: rgba(0, 212, 255, 0.1);
        padding: 1.25rem 1rem;
        vertical-align: middle;
    }

    .table tbody tr {
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background: rgba(0, 212, 255, 0.05);
        transform: scale(1.01);
    }

    .rank-cell {
        font-weight: 800;
        font-size: 1.25rem;
        color: var(--triada-primary);
        text-align: center;
        width: 80px;
    }

    .user-cell {
        font-weight: 600;
        color: var(--triada-text-primary);
    }

    .user-cell a {
        color: var(--triada-text-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .user-cell a:hover {
        color: var(--triada-primary);
    }

    .team-cell {
        color: var(--triada-text-muted);
        font-size: 0.875rem;
    }

    .score-cell {
        font-weight: 800;
        font-size: 1.25rem;
        color: var(--triada-secondary);
        text-align: right;
    }

    .rank-badge {
        display: inline-block;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--triada-gradient-primary);
        color: white;
        font-weight: 800;
        line-height: 40px;
        text-align: center;
    }

    .rank-1 .rank-badge { background: linear-gradient(135deg, #ffd700, #ffed4e); color: #000; }
    .rank-2 .rank-badge { background: linear-gradient(135deg, #c0c0c0, #e5e5e5); color: #000; }
    .rank-3 .rank-badge { background: linear-gradient(135deg, #cd7f32, #daa520); color: #fff; }

    .loading-scoreboard {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 400px;
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 4px solid rgba(0, 212, 255, 0.2);
        border-top: 4px solid var(--triada-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .scoreboard-hero h1 {
            font-size: 2.5rem;
        }
        
        .podium {
            flex-direction: column;
            align-items: center;
        }
        
        .podium-place {
            min-width: 280px;
            margin-bottom: 1rem;
        }
        
        .podium-first,
        .podium-second,
        .podium-third {
            order: unset;
        }
        
        .table-responsive {
            border-radius: var(--triada-radius-lg);
        }
        
        .scoreboard-filters {
            gap: 0.5rem;
        }
        
        .filter-btn {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
    }

    /* Animation for table rows */
    .table tbody tr {
        opacity: 0;
        animation: fadeInUp 0.6s ease-out forwards;
    }

    .table tbody tr:nth-child(1) { animation-delay: 0.1s; }
    .table tbody tr:nth-child(2) { animation-delay: 0.2s; }
    .table tbody tr:nth-child(3) { animation-delay: 0.3s; }
    .table tbody tr:nth-child(4) { animation-delay: 0.4s; }
    .table tbody tr:nth-child(5) { animation-delay: 0.5s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="scoreboard-hero">
    <div class="container">
        <h1>Scoreboard</h1>
        <p>See how you rank against other competitors in the TRIADA CTF challenge</p>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-12">
            {% include "components/errors.html" %}
        </div>
    </div>

    <!-- Filters -->
    <div class="scoreboard-filters">
        <a href="#" class="filter-btn active" data-filter="all">All Time</a>
        <a href="#" class="filter-btn" data-filter="today">Today</a>
        <a href="#" class="filter-btn" data-filter="week">This Week</a>
        <a href="#" class="filter-btn" data-filter="month">This Month</a>
    </div>

    <!-- Top 3 Podium -->
    <div class="podium" id="podium">
        <!-- Podium will be populated by JavaScript -->
    </div>

    <!-- Scoreboard Table -->
    <div class="scoreboard-table">
        <div class="table-responsive">
            <div id="scoreboard-content">
                <div class="loading-scoreboard">
                    <div class="loading-spinner"></div>
                    <h5 style="color: var(--triada-primary);">Loading Scoreboard...</h5>
                    <p style="color: var(--triada-text-muted);">Calculating rankings and scores</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter functionality
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all buttons
                filterBtns.forEach(b => b.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Load scoreboard data based on filter
                const filter = this.dataset.filter;
                loadScoreboard(filter);
            });
        });

        // Load initial scoreboard
        loadScoreboard('all');
    });

    function loadScoreboard(filter) {
        const content = document.getElementById('scoreboard-content');
        const podium = document.getElementById('podium');
        
        // Show loading state
        content.innerHTML = `
            <div class="loading-scoreboard">
                <div class="loading-spinner"></div>
                <h5 style="color: var(--triada-primary);">Loading Scoreboard...</h5>
                <p style="color: var(--triada-text-muted);">Calculating rankings and scores</p>
            </div>
        `;
        
        // Simulate API call (replace with actual API endpoint)
        setTimeout(() => {
            // Mock data - replace with actual data from API
            const mockData = [
                { rank: 1, name: "CyberNinja", team: "Team Alpha", score: 2500 },
                { rank: 2, name: "H4ck3rPro", team: "Team Beta", score: 2350 },
                { rank: 3, name: "SecurityGuru", team: "Team Gamma", score: 2200 },
                { rank: 4, name: "CodeBreaker", team: "Team Delta", score: 2100 },
                { rank: 5, name: "CryptoMaster", team: "Team Epsilon", score: 2000 }
            ];
            
            renderPodium(mockData.slice(0, 3));
            renderScoreboard(mockData);
        }, 1000);
    }

    function renderPodium(topThree) {
        const podium = document.getElementById('podium');
        
        if (topThree.length >= 3) {
            podium.innerHTML = `
                <div class="podium-place podium-second">
                    <div class="podium-rank">2</div>
                    <div class="podium-name">${topThree[1].name}</div>
                    <div class="podium-score">${topThree[1].score}</div>
                    <div class="podium-team">${topThree[1].team}</div>
                </div>
                <div class="podium-place podium-first">
                    <div class="podium-rank">1</div>
                    <div class="podium-name">${topThree[0].name}</div>
                    <div class="podium-score">${topThree[0].score}</div>
                    <div class="podium-team">${topThree[0].team}</div>
                </div>
                <div class="podium-place podium-third">
                    <div class="podium-rank">3</div>
                    <div class="podium-name">${topThree[2].name}</div>
                    <div class="podium-score">${topThree[2].score}</div>
                    <div class="podium-team">${topThree[2].team}</div>
                </div>
            `;
        }
    }

    function renderScoreboard(data) {
        const content = document.getElementById('scoreboard-content');
        
        let tableHTML = `
            <table class="table">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>User</th>
                        <th>Team</th>
                        <th class="text-right">Score</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        data.forEach(user => {
            tableHTML += `
                <tr class="rank-${user.rank}">
                    <td class="rank-cell">
                        <span class="rank-badge">${user.rank}</span>
                    </td>
                    <td class="user-cell">
                        <a href="/users/${user.name}">${user.name}</a>
                    </td>
                    <td class="team-cell">${user.team}</td>
                    <td class="score-cell">${user.score}</td>
                </tr>
            `;
        });
        
        tableHTML += `
                </tbody>
            </table>
        `;
        
        content.innerHTML = tableHTML;
    }
</script>
{% endblock %}
