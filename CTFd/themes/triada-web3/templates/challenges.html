{% extends "base.html" %}

{% block stylesheets %}
    <link rel="stylesheet" href="{{ url_for('views.themes', path='css/challenge-board.css') }}">
    <style>
        /* Challenge Board Specific Styles */
        .challenges-hero {
            background: var(--triada-gradient-dark);
            padding: 4rem 0 2rem;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }

        .challenges-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 30% 20%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(124, 58, 237, 0.15) 0%, transparent 50%);
            z-index: 1;
        }

        .challenges-hero .container {
            position: relative;
            z-index: 2;
        }

        .challenges-hero h1 {
            font-size: 4rem;
            font-weight: 900;
            text-align: center;
            margin-bottom: 1rem;
            background: var(--triada-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-transform: uppercase;
            letter-spacing: 0.1em;
        }

        .challenges-hero p {
            text-align: center;
            font-size: 1.25rem;
            color: var(--triada-text-secondary);
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        .challenge-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .stat-card {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: var(--triada-radius-lg);
            padding: 1.5rem;
            text-align: center;
            backdrop-filter: blur(10px);
            min-width: 150px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            background: var(--triada-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }

        .stat-label {
            color: var(--triada-text-muted);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-top: 0.5rem;
        }

        /* Challenge Categories Filter */
        .category-filters {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 3rem;
        }

        .category-filter {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: var(--triada-radius-md);
            padding: 0.75rem 1.5rem;
            color: var(--triada-text-secondary);
            text-decoration: none;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .category-filter:hover,
        .category-filter.active {
            background: var(--triada-gradient-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: var(--triada-shadow-md);
            text-decoration: none;
        }

        /* Challenge Grid */
        .challenges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .challenge-card {
            background: var(--triada-gradient-card);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: var(--triada-radius-lg);
            padding: 2rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .challenge-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--triada-gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .challenge-card:hover::before {
            transform: scaleX(1);
        }

        .challenge-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--triada-shadow-xl);
            border-color: rgba(0, 212, 255, 0.4);
        }

        .challenge-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .challenge-title {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--triada-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .challenge-points {
            background: var(--triada-gradient-secondary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--triada-radius-md);
            font-weight: 700;
            font-size: 1.1rem;
            min-width: 60px;
            text-align: center;
        }

        .challenge-category {
            display: inline-block;
            background: rgba(0, 212, 255, 0.2);
            color: var(--triada-primary);
            padding: 0.25rem 0.75rem;
            border-radius: var(--triada-radius-sm);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 1rem;
        }

        .challenge-description {
            color: var(--triada-text-secondary);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .challenge-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid rgba(0, 212, 255, 0.2);
        }

        .challenge-solves {
            color: var(--triada-text-muted);
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .challenge-difficulty {
            padding: 0.25rem 0.75rem;
            border-radius: var(--triada-radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .difficulty-easy { background: rgba(16, 185, 129, 0.2); color: var(--triada-success); }
        .difficulty-medium { background: rgba(245, 158, 11, 0.2); color: var(--triada-warning); }
        .difficulty-hard { background: rgba(239, 68, 68, 0.2); color: var(--triada-danger); }

        /* Loading Animation */
        .challenges-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(0, 212, 255, 0.2);
            border-top: 4px solid var(--triada-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .challenges-hero h1 {
                font-size: 2.5rem;
            }
            
            .challenges-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .challenge-stats {
                gap: 1rem;
            }
            
            .stat-card {
                min-width: 120px;
                padding: 1rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="challenges-hero">
    <div class="container">
        <h1>Challenges</h1>
        <p>Test your cybersecurity skills across various domains. From web exploitation to cryptography, reverse engineering to forensics.</p>
        
        <div class="challenge-stats">
            <div class="stat-card">
                <span class="stat-number" id="total-challenges">-</span>
                <div class="stat-label">Total Challenges</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="solved-challenges">-</span>
                <div class="stat-label">Solved</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" id="total-points">-</span>
                <div class="stat-label">Total Points</div>
            </div>
        </div>
    </div>
</div>

<!-- Challenge Modal -->
<div class="modal fade" id="challenge-window" tabindex="-1" role="dialog" aria-labelledby="challengeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: var(--triada-gradient-card); border: 1px solid rgba(0, 212, 255, 0.3); border-radius: var(--triada-radius-lg); backdrop-filter: blur(20px);">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-12">
            {% include "components/errors.html" %}
        </div>
    </div>

    <!-- Category Filters -->
    <div class="category-filters" id="category-filters">
        <a href="#" class="category-filter active" data-category="all">All Categories</a>
        <!-- Categories will be populated by JavaScript -->
    </div>

    <!-- Challenges Board -->
    <div id='challenges-board'>
        <div class="challenges-loading">
            <div class="loading-spinner"></div>
            <h5 style="color: var(--triada-primary);">Loading Challenges...</h5>
            <p style="color: var(--triada-text-muted);">Preparing your cybersecurity adventure</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Update challenge statistics
    function updateChallengeStats() {
        // This will be populated by the challenge loading script
        // For now, we'll set placeholder values
        document.getElementById('total-challenges').textContent = '0';
        document.getElementById('solved-challenges').textContent = '0';
        document.getElementById('total-points').textContent = '0';
    }

    // Initialize stats
    document.addEventListener('DOMContentLoaded', updateChallengeStats);
</script>
{% endblock %}

{% block entrypoint %}
    <script defer src="{{ url_for('views.themes', path='js/pages/challenges.js') }}"></script>
{% endblock %}
