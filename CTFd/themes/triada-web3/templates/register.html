{% extends "base.html" %}

{% block stylesheets %}
<style>
    .auth-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
        background: 
            radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
    }

    .auth-card {
        background: var(--triada-gradient-card);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-xl);
        box-shadow: var(--triada-shadow-xl);
        backdrop-filter: blur(20px);
        padding: 3rem;
        width: 100%;
        max-width: 500px;
        position: relative;
        overflow: hidden;
    }

    .auth-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--triada-gradient-primary);
    }

    .auth-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .auth-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: var(--triada-gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
    }

    .auth-subtitle {
        color: var(--triada-text-muted);
        font-size: 1rem;
        margin-bottom: 0;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        color: var(--triada-text-secondary);
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 0.875rem;
    }

    .form-control {
        background: rgba(26, 26, 46, 0.8);
        border: 2px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-md);
        color: var(--triada-text-primary);
        padding: 1rem 1.25rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        width: 100%;
    }

    .form-control:focus {
        border-color: var(--triada-primary);
        box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        background: rgba(26, 26, 46, 0.9);
        outline: none;
    }

    .form-control::placeholder {
        color: var(--triada-text-muted);
    }

    .input-group {
        position: relative;
    }

    .input-group-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--triada-text-muted);
        z-index: 10;
    }

    .input-group .form-control {
        padding-left: 3rem;
    }

    .btn-auth {
        background: var(--triada-gradient-primary);
        border: none;
        border-radius: var(--triada-radius-md);
        color: white;
        font-weight: 700;
        font-size: 1rem;
        padding: 1rem 2rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        transition: all 0.3s ease;
        width: 100%;
        position: relative;
        overflow: hidden;
    }

    .btn-auth::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-auth:hover::before {
        left: 100%;
    }

    .btn-auth:hover {
        transform: translateY(-2px);
        box-shadow: var(--triada-shadow-lg);
    }

    .auth-links {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(0, 212, 255, 0.2);
    }

    .auth-link {
        color: var(--triada-primary);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .auth-link:hover {
        color: var(--triada-primary-dark);
        text-decoration: none;
    }

    .terms-checkbox {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.5rem;
    }

    .terms-checkbox input[type="checkbox"] {
        margin-right: 0.75rem;
        margin-top: 0.25rem;
        transform: scale(1.2);
        flex-shrink: 0;
    }

    .terms-checkbox label {
        color: var(--triada-text-secondary);
        margin-bottom: 0;
        cursor: pointer;
        line-height: 1.5;
    }

    .password-strength {
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: var(--triada-radius-sm);
        background: rgba(26, 26, 46, 0.5);
        font-size: 0.875rem;
    }

    .strength-bar {
        height: 4px;
        border-radius: 2px;
        margin-bottom: 0.5rem;
        background: rgba(0, 212, 255, 0.2);
        overflow: hidden;
    }

    .strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }

    .strength-weak .strength-fill { width: 25%; background: var(--triada-danger); }
    .strength-fair .strength-fill { width: 50%; background: var(--triada-warning); }
    .strength-good .strength-fill { width: 75%; background: var(--triada-primary); }
    .strength-strong .strength-fill { width: 100%; background: var(--triada-success); }

    .strength-text {
        color: var(--triada-text-muted);
        font-size: 0.75rem;
    }

    /* Responsive Design */
    @media (max-width: 576px) {
        .auth-card {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }
        
        .auth-title {
            font-size: 2rem;
        }
    }

    /* Loading state */
    .btn-auth.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .btn-auth.loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        margin: auto;
        border: 2px solid transparent;
        border-top-color: #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card fade-in-up">
                    <div class="auth-header">
                        <h1 class="auth-title">Register</h1>
                        <p class="auth-subtitle">Join the TRIADA CTF community</p>
                    </div>

                    {% include "components/errors.html" %}

                    <form method="POST" accept-charset="utf-8" autocomplete="off" role="form" class="form-register">
                        <input name="nonce" type="hidden" value="{{ Session.nonce }}">
                        
                        <div class="form-group">
                            <label for="name" class="form-label">Username</label>
                            <div class="input-group">
                                <i class="fas fa-user input-group-icon"></i>
                                <input 
                                    class="form-control" 
                                    id="name" 
                                    name="name" 
                                    placeholder="Choose a unique username" 
                                    required 
                                    type="text" 
                                    value="{{ request.form.name }}"
                                    autocomplete="username"
                                >
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <i class="fas fa-envelope input-group-icon"></i>
                                <input 
                                    class="form-control" 
                                    id="email" 
                                    name="email" 
                                    placeholder="Enter your email address" 
                                    required 
                                    type="email" 
                                    value="{{ request.form.email }}"
                                    autocomplete="email"
                                >
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-group-icon"></i>
                                <input 
                                    class="form-control" 
                                    id="password" 
                                    name="password" 
                                    placeholder="Create a strong password" 
                                    required 
                                    type="password"
                                    autocomplete="new-password"
                                >
                            </div>
                            <div class="password-strength" id="password-strength" style="display: none;">
                                <div class="strength-bar">
                                    <div class="strength-fill"></div>
                                </div>
                                <div class="strength-text">Password strength: <span id="strength-text">Weak</span></div>
                            </div>
                        </div>

                        {% if Configs.user_mode == "teams" %}
                        <div class="form-group">
                            <label for="team" class="form-label">Team Name (Optional)</label>
                            <div class="input-group">
                                <i class="fas fa-users input-group-icon"></i>
                                <input 
                                    class="form-control" 
                                    id="team" 
                                    name="team" 
                                    placeholder="Enter team name or leave blank" 
                                    type="text" 
                                    value="{{ request.form.team }}"
                                >
                            </div>
                        </div>
                        {% endif %}

                        <div class="terms-checkbox">
                            <input type="checkbox" id="terms" name="terms" required>
                            <label for="terms">
                                I agree to the <a href="#" class="auth-link">Terms of Service</a> and 
                                <a href="#" class="auth-link">Privacy Policy</a>. I understand that this platform 
                                is for educational purposes only.
                            </label>
                        </div>

                        <button type="submit" class="btn btn-auth" id="submit">
                            <i class="fas fa-user-plus me-2"></i>
                            Create Account
                        </button>
                    </form>

                    <div class="auth-links">
                        <p class="mb-0">
                            Already have an account? 
                            <a href="{{ url_for('auth.login') }}" class="auth-link">
                                <i class="fas fa-sign-in-alt me-1"></i>Sign in here
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('.form-register');
        const submitBtn = document.getElementById('submit');
        const passwordInput = document.getElementById('password');
        const strengthIndicator = document.getElementById('password-strength');
        const strengthText = document.getElementById('strength-text');
        
        form.addEventListener('submit', function() {
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<span>Creating Account...</span>';
        });

        // Password strength checker
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = checkPasswordStrength(password);
            
            if (password.length > 0) {
                strengthIndicator.style.display = 'block';
                strengthIndicator.className = 'password-strength strength-' + strength.level;
                strengthText.textContent = strength.text;
            } else {
                strengthIndicator.style.display = 'none';
            }
        });

        function checkPasswordStrength(password) {
            let score = 0;
            let feedback = [];

            if (password.length >= 8) score++;
            if (password.match(/[a-z]/)) score++;
            if (password.match(/[A-Z]/)) score++;
            if (password.match(/[0-9]/)) score++;
            if (password.match(/[^a-zA-Z0-9]/)) score++;

            switch (score) {
                case 0:
                case 1:
                    return { level: 'weak', text: 'Weak' };
                case 2:
                    return { level: 'fair', text: 'Fair' };
                case 3:
                case 4:
                    return { level: 'good', text: 'Good' };
                case 5:
                    return { level: 'strong', text: 'Strong' };
                default:
                    return { level: 'weak', text: 'Weak' };
            }
        }

        // Add focus effects to form inputs
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    });
</script>
{% endblock %}
