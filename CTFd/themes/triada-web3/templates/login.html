{% extends "base.html" %}

{% block stylesheets %}
<style>
    .auth-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 0;
        background: 
            radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
    }

    .auth-card {
        background: var(--triada-gradient-card);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-xl);
        box-shadow: var(--triada-shadow-xl);
        backdrop-filter: blur(20px);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
        position: relative;
        overflow: hidden;
    }

    .auth-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--triada-gradient-primary);
    }

    .auth-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .auth-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: var(--triada-gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
    }

    .auth-subtitle {
        color: var(--triada-text-muted);
        font-size: 1rem;
        margin-bottom: 0;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        color: var(--triada-text-secondary);
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 0.875rem;
    }

    .form-control {
        background: rgba(26, 26, 46, 0.8);
        border: 2px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-md);
        color: var(--triada-text-primary);
        padding: 1rem 1.25rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        width: 100%;
    }

    .form-control:focus {
        border-color: var(--triada-primary);
        box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
        background: rgba(26, 26, 46, 0.9);
        outline: none;
    }

    .form-control::placeholder {
        color: var(--triada-text-muted);
    }

    .input-group {
        position: relative;
    }

    .input-group-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--triada-text-muted);
        z-index: 10;
    }

    .input-group .form-control {
        padding-left: 3rem;
    }

    .btn-auth {
        background: var(--triada-gradient-primary);
        border: none;
        border-radius: var(--triada-radius-md);
        color: white;
        font-weight: 700;
        font-size: 1rem;
        padding: 1rem 2rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        transition: all 0.3s ease;
        width: 100%;
        position: relative;
        overflow: hidden;
    }

    .btn-auth::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-auth:hover::before {
        left: 100%;
    }

    .btn-auth:hover {
        transform: translateY(-2px);
        box-shadow: var(--triada-shadow-lg);
    }

    .auth-links {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(0, 212, 255, 0.2);
    }

    .auth-link {
        color: var(--triada-primary);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .auth-link:hover {
        color: var(--triada-primary-dark);
        text-decoration: none;
    }

    .remember-me {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .remember-me input[type="checkbox"] {
        margin-right: 0.75rem;
        transform: scale(1.2);
    }

    .remember-me label {
        color: var(--triada-text-secondary);
        margin-bottom: 0;
        cursor: pointer;
    }

    .social-login {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(0, 212, 255, 0.2);
    }

    .social-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(26, 26, 46, 0.8);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: var(--triada-radius-md);
        color: var(--triada-text-secondary);
        padding: 0.75rem 1rem;
        text-decoration: none;
        transition: all 0.3s ease;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }

    .social-btn:hover {
        background: rgba(0, 212, 255, 0.1);
        border-color: var(--triada-primary);
        color: var(--triada-primary);
        text-decoration: none;
    }

    .social-btn i {
        margin-right: 0.75rem;
        font-size: 1.25rem;
    }

    /* Responsive Design */
    @media (max-width: 576px) {
        .auth-card {
            padding: 2rem 1.5rem;
            margin: 1rem;
        }
        
        .auth-title {
            font-size: 2rem;
        }
    }

    /* Loading state */
    .btn-auth.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .btn-auth.loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        margin: auto;
        border: 2px solid transparent;
        border-top-color: #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="auth-card fade-in-up">
                    <div class="auth-header">
                        <h1 class="auth-title">Login</h1>
                        <p class="auth-subtitle">Welcome back to TRIADA CTF</p>
                    </div>

                    {% include "components/errors.html" %}

                    <form method="POST" accept-charset="utf-8" autocomplete="off" role="form" class="form-signin">
                        <input name="nonce" type="hidden" value="{{ Session.nonce }}">
                        
                        <div class="form-group">
                            <label for="name" class="form-label">Username or Email</label>
                            <div class="input-group">
                                <i class="fas fa-user input-group-icon"></i>
                                <input 
                                    class="form-control" 
                                    id="name" 
                                    name="name" 
                                    placeholder="Enter your username or email" 
                                    required 
                                    type="text" 
                                    value="{{ request.form.name }}"
                                    autocomplete="username"
                                >
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-group-icon"></i>
                                <input 
                                    class="form-control" 
                                    id="password" 
                                    name="password" 
                                    placeholder="Enter your password" 
                                    required 
                                    type="password"
                                    autocomplete="current-password"
                                >
                            </div>
                        </div>

                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Remember me for 30 days</label>
                        </div>

                        <button type="submit" class="btn btn-auth" id="submit">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Sign In
                        </button>
                    </form>

                    <div class="auth-links">
                        <p class="mb-2">
                            <a href="{{ url_for('auth.reset_password') }}" class="auth-link">
                                <i class="fas fa-key me-1"></i>Forgot your password?
                            </a>
                        </p>
                        {% if registration_visible() %}
                        <p class="mb-0">
                            Don't have an account? 
                            <a href="{{ url_for('auth.register') }}" class="auth-link">
                                <i class="fas fa-user-plus me-1"></i>Create one here
                            </a>
                        </p>
                        {% endif %}
                    </div>

                    <!-- Social Login (if configured) -->
                    {% if Plugins.oauth_providers %}
                    <div class="social-login">
                        <h6 class="text-center mb-3" style="color: var(--triada-text-muted); text-transform: uppercase; letter-spacing: 0.1em;">Or sign in with</h6>
                        {% for provider in Plugins.oauth_providers %}
                        <a href="{{ url_for('auth.oauth_redirect', provider=provider.name) }}" class="social-btn">
                            <i class="fab fa-{{ provider.name }}"></i>
                            Continue with {{ provider.name.title() }}
                        </a>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('.form-signin');
        const submitBtn = document.getElementById('submit');
        
        form.addEventListener('submit', function() {
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<span>Signing In...</span>';
        });

        // Add focus effects to form inputs
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    });
</script>
{% endblock %}
