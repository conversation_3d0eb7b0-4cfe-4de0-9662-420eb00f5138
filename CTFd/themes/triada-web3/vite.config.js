const { resolve } = require("path");
import { defineConfig } from "vite";
import { CSSManifestPlugin } from "vite-manifest-css";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [CSSManifestPlugin()],
  build: {
    manifest: true,
    outDir: "static",
    rollupOptions: {
      input: {
        // JavaScript entry points
        main: resolve(__dirname, "assets/js/main.js"),
        challenges: resolve(__dirname, "assets/js/challenges.js"),
        
        // CSS entry points
        "web3-theme": resolve(__dirname, "assets/css/web3-theme.css"),
        "challenge-board": resolve(__dirname, "assets/css/challenge-board.css"),
      },
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "assets/css/variables.scss";`
      }
    }
  },
  server: {
    port: 3000,
    open: false
  }
});
